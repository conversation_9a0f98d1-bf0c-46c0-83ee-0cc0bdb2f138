# MarkItDown: Intelligent Document Processing & Code Generation Platform

[![PyPI](https://img.shields.io/pypi/v/markitdown.svg)](https://pypi.org/project/markitdown/)
![PyPI - Downloads](https://img.shields.io/pypi/dd/markitdown)
[![Built by AutoGen Team](https://img.shields.io/badge/Built%20by-AutoGen%20Team-blue)](https://github.com/microsoft/autogen)
[![Schema Generation](https://img.shields.io/badge/Schema-Generation-green)](https://github.com/microsoft/markitdown)
[![API Specs](https://img.shields.io/badge/API-Specs-orange)](https://github.com/microsoft/markitdown)
[![Code Generation](https://img.shields.io/badge/Code-Generation-purple)](https://github.com/microsoft/markitdown)
[![CI/CD Integration](https://img.shields.io/badge/CI%2FCD-Integration-red)](https://github.com/microsoft/markitdown)

> [!TIP]
> MarkItDown now offers an MCP (Model Context Protocol) server for integration with LLM applications like Claude Desktop. See [markitdown-mcp](https://github.com/microsoft/markitdown/tree/main/packages/markitdown-mcp) for more information.

> [!IMPORTANT]
> Breaking changes between 0.0.1 to 0.1.0:
> * Dependencies are now organized into optional feature-groups (further details below). Use `pip install 'markitdown[all]'` to have backward-compatible behavior.
> * convert\_stream() now requires a binary file-like object (e.g., a file opened in binary mode, or an io.BytesIO object). This is a breaking change from the previous version, where it previously also accepted text file-like objects, like io.StringIO.
> * The DocumentConverter class interface has changed to read from file-like streams rather than file paths. *No temporary files are created anymore*. If you are the maintainer of a plugin, or custom DocumentConverter, you likely need to update your code. Otherwise, if only using the MarkItDown class or CLI (as in these examples), you should not need to change anything.

**MarkItDown** is an intelligent document processing platform that leverages advanced parsing capabilities to extract structured data from technical documents and automatically generate code, documentation, and API specifications. Built on a foundation of robust document conversion technology, MarkItDown transforms the way development teams handle technical specifications, requirements documents, and legacy system documentation.

## 🎯 Product Vision

Transform technical documentation into actionable code and specifications through intelligent document processing, bridging the gap between business requirements and technical implementation.

## 📋 Table of Contents

- [⚡ Quick Start](#-quick-start)
- [🔥 Core Pain Points Solved](#-core-pain-points-solved)
- [🚀 Key Features & Capabilities](#-key-features--capabilities)
- [Why Markdown?](#why-markdown)
- [💼 Use Cases & Applications](#-use-cases--applications)
- [🏗️ Technical Architecture](#️-technical-architecture)
- [Prerequisites](#prerequisites)
- [Installation & Setup](#installation--setup)
- [Usage](#usage)
- [🔧 Advanced Usage Examples](#-advanced-usage-examples)
- [🔌 Integrations & APIs](#-integrations--apis)
- [🗺️ Roadmap & Future Features](#️-roadmap--future-features)
- [Contributing](#contributing)

## ⚡ Quick Start

```bash
# Install MarkItDown
pip install 'markitdown[all]'

# Convert a document to Markdown
markitdown document.pdf > output.md

# Extract schemas and generate code (Advanced)
markitdown document.pdf --extract-schemas --generate-openapi --output-dir ./generated
```

```python
# Python API
from markitdown import MarkItDown

md = MarkItDown()
result = md.convert("technical_spec.docx")
print(result.markdown)
```

## 🔥 Core Pain Points Solved

- **Manual Implementation Overhead**: Technical specifications in PDFs/Word docs require time-consuming manual code implementation
- **Documentation Drift**: API documentation becomes outdated and inconsistent with actual code over time
- **Requirements Translation Gap**: Business requirements documents don't translate easily to technical specifications
- **Legacy System Knowledge**: Critical system documentation exists only in non-searchable, outdated formats
- **Compliance Tracking**: Difficulty mapping between requirements and actual implementation for auditing

## 🚀 Key Features & Capabilities

### Smart Document Processing
- **Multi-Format Support**: PDF, PowerPoint, Word, Excel, Images (EXIF + OCR), Audio (transcription), HTML, CSV, JSON, XML, ZIP files, YouTube URLs, EPubs, and more
- **Intelligent Data Extraction**: Advanced NLP processing pipeline for technical document analysis
- **Structure Preservation**: Maintains document hierarchy, tables, lists, and formatting in Markdown output
- **Metadata Extraction**: Captures document properties, creation dates, and embedded information

### Code & Schema Generation
- **Database Schema Generation**: Extract data dictionaries and generate SQL DDL, JSON Schema, and Protocol Buffer definitions
- **API Specification Generation**: Automatically create OpenAPI/Swagger specifications from API documentation
- **Test Case Generation**: Convert requirement specifications into executable test cases and validation scripts
- **Multi-Language Support**: Code generation templates for Python, JavaScript, Java, C#, Go, and more

### Development Integration
- **Version Control Integration**: Git hooks and automated documentation updates with change tracking
- **CI/CD Pipeline Integration**: Validation workflows and automated compliance checking
- **IDE Integration**: Plugins for popular development environments (VS Code, IntelliJ, etc.)
- **RESTful APIs**: Programmatic access for integration with development tools and workflows

### Advanced Features
- **Custom Template System**: Organization-specific patterns and code generation templates
- **Compliance Mapping**: Track relationships between requirements and implementation
- **Multi-Format Output**: Generate JSON Schema, Protocol Buffers, SQL DDL, and custom formats
- **Batch Processing**: Handle large document sets with parallel processing capabilities

### Supported Input Formats
- **Documents**: PDF, Word (.docx), PowerPoint (.pptx), Excel (.xlsx/.xls)
- **Media**: Images (with OCR), Audio files (with transcription), Video files
- **Web Content**: HTML pages, RSS feeds, YouTube videos, Wikipedia articles
- **Data Formats**: CSV, JSON, XML, EPUB, Jupyter Notebooks
- **Archives**: ZIP files with recursive content processing
- **Legacy Formats**: Outlook messages, various text-based formats

## Why Markdown?

Markdown is extremely close to plain text, with minimal markup or formatting, but still
provides a way to represent important document structure. Mainstream LLMs, such as
OpenAI's GPT-4o, natively "_speak_" Markdown, and often incorporate Markdown into their
responses unprompted. This suggests that they have been trained on vast amounts of
Markdown-formatted text, and understand it well. As a side benefit, Markdown conventions
are also highly token-efficient.

## 💼 Use Cases & Applications

### Enterprise Development Teams
- **API-First Development**: Convert API documentation PDFs into OpenAPI specifications for immediate implementation
- **Legacy System Modernization**: Extract database schemas and business rules from outdated documentation
- **Compliance Documentation**: Automatically generate audit trails linking requirements to code implementation
- **Technical Debt Reduction**: Convert scattered documentation into searchable, version-controlled formats

### DevOps & Platform Engineering
- **Infrastructure as Code**: Generate Terraform/CloudFormation templates from infrastructure documentation
- **CI/CD Pipeline Generation**: Create deployment scripts and validation workflows from operational procedures
- **Monitoring & Alerting**: Extract SLA requirements and generate monitoring configurations
- **Documentation Automation**: Keep technical documentation synchronized with code changes

### Product & Engineering Management
- **Requirements Traceability**: Map business requirements to technical specifications and test cases
- **Sprint Planning**: Convert feature specifications into actionable development tasks
- **Technical Specification Review**: Validate completeness and consistency of technical documents
- **Cross-Team Collaboration**: Standardize documentation formats across different teams and tools

### Quality Assurance & Testing
- **Test Case Generation**: Automatically create test scenarios from requirement documents
- **Validation Scripts**: Generate automated tests from API specifications and data schemas
- **Compliance Testing**: Create audit scripts that verify implementation against requirements
- **Regression Testing**: Maintain test coverage as documentation and requirements evolve

### Data Engineering & Analytics
- **Schema Evolution**: Track and manage database schema changes from documentation
- **Data Pipeline Documentation**: Generate data flow diagrams and processing specifications
- **ETL Process Generation**: Create data transformation scripts from business rule documents
- **Data Governance**: Maintain data lineage and compliance documentation

## 🏗️ Technical Architecture

### Core Processing Pipeline
```
Document Input → Format Detection → Content Extraction → NLP Analysis → Structure Recognition → Output Generation
```

#### Document Processing Engine
- **Multi-Format Parsers**: Specialized converters for each supported file type with format-specific optimizations
- **Content Extraction**: Advanced text extraction preserving document structure, metadata, and relationships
- **Format Detection**: Intelligent MIME type detection and file format identification
- **Stream Processing**: Memory-efficient processing of large documents without temporary files

#### NLP & Analysis Pipeline
- **Document Structure Analysis**: Hierarchical content recognition (headings, sections, tables, lists)
- **Semantic Content Extraction**: Identification of schemas, APIs, data models, and business rules
- **Entity Recognition**: Detection of technical entities (database tables, API endpoints, data types)
- **Relationship Mapping**: Understanding connections between different document sections and concepts

#### Code Generation Framework
- **Template Engine**: Extensible template system supporting multiple programming languages and frameworks
- **Schema Generators**: Automated creation of database schemas, JSON Schema, Protocol Buffers, and OpenAPI specs
- **Test Case Generation**: Intelligent creation of unit tests, integration tests, and validation scripts
- **Custom Output Formats**: Pluggable output system for organization-specific requirements

#### Integration Layer
- **Version Control Integration**: Git hooks, automated commits, and change tracking for generated artifacts
- **CI/CD Pipeline Support**: Integration with Jenkins, GitHub Actions, GitLab CI, and other automation platforms
- **IDE Extensions**: Real-time document processing within development environments
- **RESTful API**: Comprehensive API for programmatic access and third-party integrations

#### Plugin Architecture
- **Extensible Converter System**: Custom document converters for proprietary formats
- **Processing Plugins**: Custom analysis and extraction logic for domain-specific requirements
- **Output Plugins**: Custom generators for specialized output formats and integrations
- **Third-Party Integrations**: Seamless connection with external tools and services

## Prerequisites
MarkItDown requires Python 3.10 or higher. It is recommended to use a virtual environment to avoid dependency conflicts.

With the standard Python installation, you can create and activate a virtual environment using the following commands:

```bash
python -m venv .venv
source .venv/bin/activate
```

If using `uv`, you can create a virtual environment with:

```bash
uv venv --python=3.12 .venv
source .venv/bin/activate
# NOTE: Be sure to use 'uv pip install' rather than just 'pip install' to install packages in this virtual environment
```

If you are using Anaconda, you can create a virtual environment with:

```bash
conda create -n markitdown python=3.12
conda activate markitdown
```

## Installation & Setup

### Quick Start Installation

For basic document conversion capabilities:
```bash
pip install 'markitdown[all]'
```

For development and advanced features:
```bash
<NAME_EMAIL>:microsoft/markitdown.git
cd markitdown
pip install -e 'packages/markitdown[all]'
```

### Feature-Specific Installation

Install only the components you need for better dependency management:

#### Core Document Processing
```bash
# Basic text and web content processing
pip install markitdown

# PDF processing with advanced extraction
pip install 'markitdown[pdf,ai-extraction]'

# Office document processing
pip install 'markitdown[docx,pptx,xlsx]'

# Media processing with transcription
pip install 'markitdown[audio-transcription,image-ocr]'
```

#### Advanced Features
```bash
# Code generation and schema extraction
pip install 'markitdown[codegen,schema-tools]'

# NLP processing and semantic analysis
pip install 'markitdown[nlp,semantic-analysis]'

# Development tool integrations
pip install 'markitdown[git-integration,ci-cd-tools]'

# Enterprise features (API server, batch processing)
pip install 'markitdown[enterprise,api-server]'
```

### Development Environment Setup

#### For Plugin Development
```bash
# Install development dependencies
pip install 'markitdown[dev,testing]'

# Set up pre-commit hooks
pre-commit install

# Run tests to verify installation
pytest packages/markitdown/tests/
```

#### For Custom Template Development
```bash
# Install template development tools
pip install 'markitdown[template-dev]'

# Initialize template workspace
markitdown init-templates --workspace ./my-templates

# Validate template syntax
markitdown validate-templates ./my-templates
```

### Configuration

#### Environment Variables
```bash
# Enable advanced NLP processing
export MARKITDOWN_ENABLE_NLP=true

# Configure code generation templates
export MARKITDOWN_TEMPLATE_PATH=/path/to/custom/templates

# Set up Git integration
export MARKITDOWN_GIT_AUTO_COMMIT=true
export MARKITDOWN_GIT_COMMIT_MESSAGE="docs: auto-update from MarkItDown"

# Configure API server
export MARKITDOWN_API_HOST=0.0.0.0
export MARKITDOWN_API_PORT=8080
```

#### Configuration File
Create `markitdown.config.yaml` in your project root:
```yaml
processing:
  enable_nlp: true
  preserve_structure: true
  extract_schemas: true

generation:
  template_path: "./templates"
  output_formats: ["json-schema", "openapi", "sql-ddl"]
  target_languages: ["python", "typescript", "java"]

integrations:
  git:
    auto_commit: true
    commit_message: "docs: auto-update from MarkItDown"
  ci_cd:
    validate_output: true
    run_tests: true
```

## Usage

### Command-Line

```bash
markitdown path-to-file.pdf > document.md
```

Or use `-o` to specify the output file:

```bash
markitdown path-to-file.pdf -o document.md
```

You can also pipe content:

```bash
cat path-to-file.pdf | markitdown
```

### Optional Dependencies
MarkItDown has optional dependencies for activating various file formats. Earlier in this document, we installed all optional dependencies with the `[all]` option. However, you can also install them individually for more control. For example:

```bash
pip install 'markitdown[pdf, docx, pptx]'
```

will install only the dependencies for PDF, DOCX, and PPTX files.

At the moment, the following optional dependencies are available:

* `[all]` Installs all optional dependencies
* `[pptx]` Installs dependencies for PowerPoint files
* `[docx]` Installs dependencies for Word files
* `[xlsx]` Installs dependencies for Excel files
* `[xls]` Installs dependencies for older Excel files
* `[pdf]` Installs dependencies for PDF files
* `[outlook]` Installs dependencies for Outlook messages
* `[az-doc-intel]` Installs dependencies for Azure Document Intelligence
* `[audio-transcription]` Installs dependencies for audio transcription of wav and mp3 files
* `[youtube-transcription]` Installs dependencies for fetching YouTube video transcription

### Plugins

MarkItDown also supports 3rd-party plugins. Plugins are disabled by default. To list installed plugins:

```bash
markitdown --list-plugins
```

To enable plugins use:

```bash
markitdown --use-plugins path-to-file.pdf
```

To find available plugins, search GitHub for the hashtag `#markitdown-plugin`. To develop a plugin, see `packages/markitdown-sample-plugin`.

### Azure Document Intelligence

To use Microsoft Document Intelligence for conversion:

```bash
markitdown path-to-file.pdf -o document.md -d -e "<document_intelligence_endpoint>"
```

More information about how to set up an Azure Document Intelligence Resource can be found [here](https://learn.microsoft.com/en-us/azure/ai-services/document-intelligence/how-to-guides/create-document-intelligence-resource?view=doc-intel-4.0.0)

### Python API

Basic usage in Python:

```python
from markitdown import MarkItDown

md = MarkItDown(enable_plugins=False) # Set to True to enable plugins
result = md.convert("test.xlsx")
print(result.text_content)
```

Document Intelligence conversion in Python:

```python
from markitdown import MarkItDown

md = MarkItDown(docintel_endpoint="<document_intelligence_endpoint>")
result = md.convert("test.pdf")
print(result.text_content)
```

To use Large Language Models for image descriptions, provide `llm_client` and `llm_model`:

```python
from markitdown import MarkItDown
from openai import OpenAI

client = OpenAI()
md = MarkItDown(llm_client=client, llm_model="gpt-4o")
result = md.convert("example.jpg")
print(result.text_content)
```

### Docker

```sh
docker build -t markitdown:latest .
docker run --rm -i markitdown:latest < ~/your-file.pdf > output.md
```

## 🔧 Advanced Usage Examples

### Schema Extraction & Generation

#### Database Schema from Data Dictionary
```python
from markitdown import MarkItDown
from markitdown.generators import DatabaseSchemaGenerator

# Extract schema from data dictionary document
md = MarkItDown(enable_schema_extraction=True)
result = md.convert("data_dictionary.pdf")

# Generate SQL DDL
schema_gen = DatabaseSchemaGenerator()
sql_ddl = schema_gen.generate_sql(result.extracted_schemas)
print(sql_ddl)

# Generate JSON Schema
json_schema = schema_gen.generate_json_schema(result.extracted_schemas)
with open("api_schema.json", "w") as f:
    json.dump(json_schema, f, indent=2)
```

#### API Specification Generation
```python
from markitdown import MarkItDown
from markitdown.generators import OpenAPIGenerator

# Convert API documentation to OpenAPI spec
md = MarkItDown(enable_api_extraction=True)
result = md.convert("api_documentation.docx")

# Generate OpenAPI specification
api_gen = OpenAPIGenerator()
openapi_spec = api_gen.generate_spec(result.extracted_apis)

# Save as YAML
with open("api_spec.yaml", "w") as f:
    yaml.dump(openapi_spec, f)
```

### Test Case Generation

#### Automated Test Creation from Requirements
```python
from markitdown import MarkItDown
from markitdown.generators import TestCaseGenerator

# Extract test scenarios from requirements document
md = MarkItDown(enable_test_extraction=True)
result = md.convert("requirements_specification.pdf")

# Generate test cases
test_gen = TestCaseGenerator(framework="pytest")
test_cases = test_gen.generate_tests(result.extracted_requirements)

# Save test files
for test_file, content in test_cases.items():
    with open(f"tests/{test_file}", "w") as f:
        f.write(content)
```

### Batch Processing & Automation

#### Process Multiple Documents
```python
from markitdown import MarkItDown
from markitdown.batch import BatchProcessor
import asyncio

async def process_document_set():
    processor = BatchProcessor(
        enable_schema_extraction=True,
        enable_api_extraction=True,
        output_formats=["json-schema", "openapi", "sql-ddl"]
    )

    # Process entire directory
    results = await processor.process_directory(
        input_dir="./technical_docs",
        output_dir="./generated_artifacts",
        file_patterns=["*.pdf", "*.docx", "*.pptx"]
    )

    return results

# Run batch processing
results = asyncio.run(process_document_set())
```

### Git Integration & Automation

#### Automated Documentation Updates
```python
from markitdown import MarkItDown
from markitdown.integrations import GitIntegration

# Set up Git integration
git_integration = GitIntegration(
    repo_path="./my_project",
    auto_commit=True,
    commit_message="docs: update from technical specifications"
)

# Process and commit changes
md = MarkItDown(git_integration=git_integration)
result = md.convert_and_commit("updated_specs.pdf")

# Generate pull request
git_integration.create_pull_request(
    title="Update API specifications",
    description="Automated update from technical documentation"
)
```

### CI/CD Pipeline Integration

#### GitHub Actions Workflow
```yaml
# .github/workflows/docs-update.yml
name: Update Documentation
on:
  push:
    paths: ['docs/**/*.pdf', 'docs/**/*.docx']

jobs:
  update-docs:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install MarkItDown
        run: pip install 'markitdown[all,ci-cd]'

      - name: Process Documents
        run: |
          markitdown batch-process \
            --input-dir ./docs \
            --output-dir ./generated \
            --formats json-schema,openapi,sql-ddl \
            --validate-output

      - name: Commit Changes
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add generated/
          git commit -m "docs: auto-update generated artifacts" || exit 0
          git push
```

### Custom Template Development

#### Creating Organization-Specific Templates
```python
from markitdown.templates import TemplateEngine, CustomTemplate

# Define custom template for your organization
class MyOrgAPITemplate(CustomTemplate):
    def __init__(self):
        super().__init__(
            name="myorg-api",
            description="Custom API template for MyOrg standards"
        )

    def generate(self, extracted_data):
        # Custom generation logic
        return {
            "openapi": self.generate_openapi(extracted_data),
            "client_sdk": self.generate_client_sdk(extracted_data),
            "documentation": self.generate_docs(extracted_data)
        }

# Register and use custom template
engine = TemplateEngine()
engine.register_template(MyOrgAPITemplate())

# Use in processing
md = MarkItDown(template_engine=engine)
result = md.convert("api_spec.pdf", template="myorg-api")
```

## 🔌 Integrations & APIs

### RESTful API Server

#### Starting the API Server
```bash
# Start with default configuration
markitdown serve

# Start with custom configuration
markitdown serve --host 0.0.0.0 --port 8080 --workers 4

# Start with advanced features enabled
markitdown serve --enable-schema-extraction --enable-batch-processing
```

#### API Endpoints

**Document Conversion**
```bash
# Convert single document
curl -X POST "http://localhost:8080/api/v1/convert" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@document.pdf" \
  -F "output_format=markdown"

# Convert with schema extraction
curl -X POST "http://localhost:8080/api/v1/convert" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@api_spec.docx" \
  -F "extract_schemas=true" \
  -F "generate_openapi=true"
```

**Batch Processing**
```bash
# Submit batch job
curl -X POST "http://localhost:8080/api/v1/batch" \
  -H "Content-Type: application/json" \
  -d '{
    "documents": ["doc1.pdf", "doc2.docx"],
    "output_formats": ["json-schema", "openapi"],
    "callback_url": "https://myapp.com/webhook"
  }'

# Check batch status
curl "http://localhost:8080/api/v1/batch/{job_id}/status"
```

**Schema & Code Generation**
```bash
# Generate database schema
curl -X POST "http://localhost:8080/api/v1/generate/schema" \
  -H "Content-Type: application/json" \
  -d '{
    "source_document": "data_dictionary.pdf",
    "schema_type": "postgresql",
    "include_constraints": true
  }'

# Generate API client
curl -X POST "http://localhost:8080/api/v1/generate/client" \
  -H "Content-Type: application/json" \
  -d '{
    "openapi_spec": "api_spec.yaml",
    "language": "python",
    "package_name": "my_api_client"
  }'
```

### IDE Integrations

#### Visual Studio Code Extension
```bash
# Install the extension
code --install-extension markitdown.vscode-markitdown

# Use in VS Code
# 1. Right-click on document file
# 2. Select "MarkItDown: Convert to Markdown"
# 3. Choose output options (schema extraction, code generation)
```

#### IntelliJ IDEA Plugin
```bash
# Install via JetBrains Marketplace
# Search for "MarkItDown" in Settings > Plugins

# Usage:
# Tools > MarkItDown > Convert Document
# Tools > MarkItDown > Extract Schema
# Tools > MarkItDown > Generate Tests
```

#### Vim/Neovim Plugin
```lua
-- Add to your init.lua
require('markitdown').setup({
  auto_convert = true,
  extract_schemas = true,
  output_dir = './generated'
})

-- Usage:
-- :MarkItDown convert current_file.pdf
-- :MarkItDown extract-schema data_dict.xlsx
-- :MarkItDown generate-tests requirements.docx
```

### Development Tool Integrations

#### Postman Integration
```javascript
// Postman pre-request script
pm.sendRequest({
    url: 'http://localhost:8080/api/v1/convert',
    method: 'POST',
    header: {
        'Content-Type': 'multipart/form-data'
    },
    body: {
        mode: 'formdata',
        formdata: [
            {key: 'file', src: 'api_documentation.pdf', type: 'file'},
            {key: 'generate_openapi', value: 'true', type: 'text'}
        ]
    }
}, function (err, response) {
    if (!err) {
        const openapi_spec = response.json().generated_artifacts.openapi;
        pm.globals.set("api_spec", JSON.stringify(openapi_spec));
    }
});
```

#### Swagger/OpenAPI Integration
```yaml
# swagger-codegen-config.yaml
markitdown_integration:
  enabled: true
  source_documents:
    - "docs/api_specification.pdf"
    - "docs/data_models.docx"
  auto_update: true
  validation:
    strict_mode: true
    check_completeness: true
```

### CI/CD Platform Integrations

#### Jenkins Pipeline
```groovy
pipeline {
    agent any

    stages {
        stage('Process Documentation') {
            steps {
                script {
                    sh '''
                        markitdown batch-process \
                            --input-dir ./docs \
                            --output-dir ./generated \
                            --formats json-schema,openapi,sql-ddl \
                            --validate-output \
                            --fail-on-error
                    '''
                }
            }
        }

        stage('Generate Code') {
            steps {
                script {
                    sh '''
                        markitdown generate-client \
                            --spec ./generated/api_spec.yaml \
                            --language python \
                            --output ./src/api_client
                    '''
                }
            }
        }

        stage('Run Generated Tests') {
            steps {
                script {
                    sh '''
                        python -m pytest ./generated/tests/ -v
                    '''
                }
            }
        }
    }
}
```

#### GitLab CI Integration
```yaml
# .gitlab-ci.yml
stages:
  - process-docs
  - generate-code
  - test

process-documentation:
  stage: process-docs
  image: python:3.11
  before_script:
    - pip install 'markitdown[all]'
  script:
    - markitdown batch-process --input-dir docs/ --output-dir generated/
  artifacts:
    paths:
      - generated/
    expire_in: 1 hour

generate-api-client:
  stage: generate-code
  dependencies:
    - process-documentation
  script:
    - markitdown generate-client --spec generated/api_spec.yaml --language typescript
  artifacts:
    paths:
      - src/api-client/

validate-generated-code:
  stage: test
  dependencies:
    - generate-api-client
  script:
    - npm test
    - python -m pytest generated/tests/
```

### Webhook & Event Integration

#### Webhook Configuration
```python
from markitdown.webhooks import WebhookManager

# Set up webhook for document processing events
webhook_manager = WebhookManager()

@webhook_manager.on('document_processed')
def handle_document_processed(event):
    """Handle document processing completion"""
    document_id = event['document_id']
    artifacts = event['generated_artifacts']

    # Notify external systems
    notify_slack(f"Document {document_id} processed successfully")
    update_project_management_tool(artifacts)

@webhook_manager.on('schema_extracted')
def handle_schema_extracted(event):
    """Handle schema extraction completion"""
    schema = event['extracted_schema']

    # Auto-update database
    apply_schema_migration(schema)

    # Update API documentation
    update_api_docs(schema)
```

#### Event-Driven Workflows
```python
from markitdown.events import EventBus
from markitdown.workflows import WorkflowEngine

# Define workflow for document processing
workflow = WorkflowEngine()

@workflow.step('extract_content')
def extract_content(document_path):
    md = MarkItDown()
    return md.convert(document_path)

@workflow.step('extract_schemas')
def extract_schemas(content):
    extractor = SchemaExtractor()
    return extractor.extract(content)

@workflow.step('generate_code')
def generate_code(schemas):
    generator = CodeGenerator()
    return generator.generate_all(schemas)

@workflow.step('run_tests')
def run_tests(generated_code):
    test_runner = TestRunner()
    return test_runner.run_all(generated_code)

# Execute workflow
result = workflow.execute([
    'extract_content',
    'extract_schemas',
    'generate_code',
    'run_tests'
], input_data={'document_path': 'specification.pdf'})
```

## 🗺️ Roadmap & Future Features

### Current Development (Q1 2025)

#### Enhanced NLP Processing
- **Semantic Understanding**: Advanced context-aware document analysis for better entity extraction
- **Multi-Language Support**: Processing documents in multiple languages with automatic translation
- **Domain-Specific Models**: Specialized processing for legal, medical, financial, and technical documents
- **Relationship Mapping**: Intelligent detection of cross-document references and dependencies

#### Advanced Code Generation
- **Framework-Specific Templates**: Support for React, Angular, Vue.js, Spring Boot, Django, and more
- **Microservices Architecture**: Generate complete microservice scaffolding from architectural documents
- **Infrastructure as Code**: Terraform, CloudFormation, and Kubernetes manifests from infrastructure specs
- **Security Integration**: Automatic security policy generation and compliance checking

### Planned Features (Q2-Q3 2025)

#### AI-Powered Enhancements
- **Document Intelligence**: GPT-4 integration for advanced document understanding and summarization
- **Code Review Integration**: Automatic validation of generated code against best practices
- **Natural Language Queries**: Ask questions about document content and get intelligent responses
- **Automated Documentation**: Generate comprehensive documentation from code and specifications

#### Enterprise Features
- **Multi-Tenant Architecture**: Support for enterprise deployments with role-based access control
- **Audit & Compliance**: Complete audit trails and compliance reporting for regulated industries
- **Performance Optimization**: Distributed processing for large-scale document processing
- **Advanced Analytics**: Insights into document processing patterns and code generation metrics

#### Developer Experience
- **Visual Studio Integration**: Full-featured extension with real-time processing and preview
- **Browser Extension**: Process web-based documents directly from the browser
- **Mobile Apps**: iOS and Android apps for document processing on the go
- **Collaborative Features**: Team workspaces and shared template libraries

### Long-Term Vision (Q4 2025 & Beyond)

#### Intelligent Automation
- **Self-Learning Templates**: Templates that improve based on usage patterns and feedback
- **Predictive Processing**: Anticipate document processing needs based on project patterns
- **Automated Refactoring**: Intelligent code updates when specifications change
- **Cross-Platform Synchronization**: Seamless integration across all development tools and platforms

#### Advanced Integrations
- **Low-Code Platforms**: Integration with Zapier, Microsoft Power Platform, and similar tools
- **Enterprise Systems**: Direct integration with SAP, Salesforce, ServiceNow, and other enterprise platforms
- **Version Control Evolution**: Advanced Git integration with semantic versioning and automated merging
- **Cloud-Native Features**: Native support for AWS, Azure, and GCP services and deployment patterns

#### Innovation Areas
- **Blockchain Integration**: Smart contract generation from legal documents and specifications
- **IoT & Edge Computing**: Processing specifications for IoT devices and edge computing scenarios
- **AR/VR Documentation**: Immersive documentation experiences and 3D visualization of system architectures
- **Quantum Computing**: Support for quantum algorithm specifications and quantum circuit generation

### Community & Ecosystem

#### Open Source Initiatives
- **Plugin Marketplace**: Community-driven marketplace for custom converters and generators
- **Template Library**: Extensive library of community-contributed templates
- **Integration Partnerships**: Official partnerships with major development tool vendors
- **Educational Resources**: Comprehensive tutorials, courses, and certification programs

#### Research & Development
- **Academic Partnerships**: Collaboration with universities on document processing research
- **Industry Standards**: Contributing to open standards for document processing and code generation
- **Performance Benchmarks**: Establishing industry benchmarks for document processing efficiency
- **Accessibility Features**: Ensuring full accessibility compliance and support for assistive technologies

### Getting Involved

We welcome contributions to help realize this roadmap! Here's how you can get involved:

- **Feature Requests**: Submit ideas and vote on proposed features in our GitHub Discussions
- **Beta Testing**: Join our beta program to test new features before release
- **Plugin Development**: Create custom plugins for specialized use cases
- **Documentation**: Help improve documentation and create tutorials
- **Community Support**: Answer questions and help other users in our community forums

**Interested in enterprise features or custom development?** Contact us at [<EMAIL>](mailto:<EMAIL>)

## Contributing

This project welcomes contributions and suggestions. Most contributions require you to agree to a
Contributor License Agreement (CLA) declaring that you have the right to, and actually do, grant us
the rights to use your contribution. For details, visit https://cla.opensource.microsoft.com.

When you submit a pull request, a CLA bot will automatically determine whether you need to provide
a CLA and decorate the PR appropriately (e.g., status check, comment). Simply follow the instructions
provided by the bot. You will only need to do this once across all repos using our CLA.

This project has adopted the [Microsoft Open Source Code of Conduct](https://opensource.microsoft.com/codeofconduct/).
For more information see the [Code of Conduct FAQ](https://opensource.microsoft.com/codeofconduct/faq/) or
contact [<EMAIL>](mailto:<EMAIL>) with any additional questions or comments.

### How to Contribute

You can help by looking at issues or helping review PRs. Any issue or PR is welcome, but we have also marked some as 'open for contribution' and 'open for reviewing' to help facilitate community contributions. These are ofcourse just suggestions and you are welcome to contribute in any way you like.

<div align="center">

|            | All                                                          | Especially Needs Help from Community                                                                                                      |
| ---------- | ------------------------------------------------------------ | ----------------------------------------------------------------------------------------------------------------------------------------- |
| **Issues** | [All Issues](https://github.com/microsoft/markitdown/issues) | [Issues open for contribution](https://github.com/microsoft/markitdown/issues?q=is%3Aissue+is%3Aopen+label%3A%22open+for+contribution%22) |
| **PRs**    | [All PRs](https://github.com/microsoft/markitdown/pulls)     | [PRs open for reviewing](https://github.com/microsoft/markitdown/pulls?q=is%3Apr+is%3Aopen+label%3A%22open+for+reviewing%22)              |

</div>

### Running Tests and Checks

- Navigate to the MarkItDown package:

  ```sh
  cd packages/markitdown
  ```

- Install `hatch` in your environment and run tests:

  ```sh
  pip install hatch  # Other ways of installing hatch: https://hatch.pypa.io/dev/install/
  hatch shell
  hatch test
  ```

  (Alternative) Use the Devcontainer which has all the dependencies installed:

  ```sh
  # Reopen the project in Devcontainer and run:
  hatch test
  ```

- Run pre-commit checks before submitting a PR: `pre-commit run --all-files`

### Contributing 3rd-party Plugins

You can also contribute by creating and sharing 3rd party plugins. See `packages/markitdown-sample-plugin` for more details.

## Trademarks

This project may contain trademarks or logos for projects, products, or services. Authorized use of Microsoft
trademarks or logos is subject to and must follow
[Microsoft's Trademark & Brand Guidelines](https://www.microsoft.com/en-us/legal/intellectualproperty/trademarks/usage/general).
Use of Microsoft trademarks or logos in modified versions of this project must not cause confusion or imply Microsoft sponsorship.
Any use of third-party trademarks or logos are subject to those third-party's policies.
